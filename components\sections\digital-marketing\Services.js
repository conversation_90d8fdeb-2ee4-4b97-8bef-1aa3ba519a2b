"use client";
import Link from "next/link";
import { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { cityServicesData } from "@/data/services-city/services-city.js"; // Import city data

// Default services data (fallback)
const defaultServicesData = [
  {
    tabTitle: "Business Automation",
    title: "OMX Sync",
    description: "Your all-in-one team and operations management tool.",
    features: [
      "QR & Geo-based Attendance",
      "Leave Requests & Approvals",
      "Time Efficiency",
      "Real-time Task Management",
      "Internal Workflows & Notifications"
    ],
    whyItMatters:
      "Why it matters: No spreadsheets. No confusion. Just clarity, compliance, and productivity.",
    link: "/omx-sync",
    imageClass: ""
  },
  {
    tabTitle: "WhatsApp Automation",
    title: "OMX Flow",
    description: "Automate conversations, support & commerce 24/7 using AI.",
    features: [
      "Bulk marketing campaigns & chatbot flows",
      "Human + AI hybrid chat system",
      "Payment & CRM integrations",
      "WhatsApp API Integration",
      "AI-Powered Lead Capture & Follow-up",
      "Auto-Responders & Drip Sequences",
      "Funnel Analytics & Conversion Tracking"
    ],
    whyItMatters:
      "Why it matters: Automate conversations, nurture leads, and turn chats into conversions—at scale",
    link: "/omx-flow",
    imageClass: "img-2"
  },
  {
    tabTitle: "360° Sales Automations",
    title: "OMX Sales",
    description:
      "Your AI-powered command center for leads, ads, and communication.",
    features: [
      "WhatsApp API, SMS & Email automation",
      "CRM, cloud calling & lead tracking",
      "Ad launcher with AI-based targeting",
      "UTM analytics & smart reporting"
    ],
    whyItMatters:
      "Why it matters: Every click matters. We make sure every visitor becomes a customer.",
    link: "/omx-sales",
    imageClass: "img-3"
  },
  {
    tabTitle: "Website & Funnel Design",
    title: "Website & Funnel Design",
    description:
      "We build sales-ready websites that convert clicks into customers.",
    features: [
      "AI-integrated landing pages",
      "SEO, speed & mobile optimization",
      "WhatsApp & CRM integration",
      "Funnels with automation built-in"
    ],
    whyItMatters: null,
    link: "/websitedesigning",
    imageClass: "img-4"
  },
  {
    tabTitle: "Performance-Driven Digital Marketing",
    title: "Performance-Driven Digital Marketing",
    description:
      "From brand to bottom-funnel, we run marketing that sells, not just 'gets views.'",
    features: [
      "Branding & Storytelling",
      "Paid Ads (Google, Meta, LinkedIn)",
      "Video Production & Content Creation",
      "SEO, Funnel Strategy & Retargeting"
    ],
    whyItMatters: null,
    link: "/digitalmarketing",
    imageClass: "img-5"
  }
];

// Function to get services data based on city
export const getServicesDataByCity = (city) => {
  if (!city) return defaultServicesData;
  
  const cityKey = city.toUpperCase();
  return cityServicesData[cityKey] || defaultServicesData;
};

// Function to get available cities
export const getAvailableCities = () => {
  return Object.keys(cityServicesData);
};

export default function Services({ city = null, customHeading = "What We Do Best" }) {
  // Get services data based on city prop
  const cityData = getServicesDataByCity(city);
  const servicesData = cityData.services;
  const cityDescription = cityData.description;
  
  const [activeIndex, setActiveIndex] = useState(0);
  const handleOnClick = (index) => setActiveIndex(index);

  const [sectionRef, sectionInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [headingRef, headingInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [tabsRef, tabsInView] = useInView({ triggerOnce: true, threshold: 0.1 });
  const [contentRef, contentInView] = useInView({ triggerOnce: true, threshold: 0.1 });

  const fadeIn = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { duration: 0.8 } } };
  const fadeInUp = { hidden: { y: 50, opacity: 0 }, visible: { y: 0, opacity: 1, transition: { duration: 0.6 } } };
  const buttonVariant = { hidden: { y: 20, opacity: 0 }, visible: { y: 0, opacity: 1, transition: { duration: 0.4 } } };
  const contentAnimation = { hidden: { opacity: 0, x: 20 }, visible: { opacity: 1, x: 0, transition: { duration: 0.6 } } };
  const listAnimation = { hidden: { opacity: 0 }, visible: { opacity: 1, transition: { staggerChildren: 0.1, delayChildren: 0.3 } } };
  const listItemAnimation = { hidden: { opacity: 0, x: -20 }, visible: { opacity: 1, x: 0, transition: { duration: 0.3 } } };
  const imageAnimation = { hidden: { opacity: 0, scale: 0.9 }, visible: { opacity: 1, scale: 1, transition: { duration: 0.8 } } };

  return (
    <motion.section
      className="section-box pb-10"
      ref={sectionRef}
      id="services"
      initial="hidden"
      animate={sectionInView ? "visible" : "hidden"}
      variants={fadeIn}
    >
      <div className="box-preparing-inner">
        <div className="container">
          <div className="text-center">
            <motion.h2
              className="heading-2 mb-20"
              ref={headingRef}
              initial="hidden"
              animate={headingInView ? "visible" : "hidden"}
              variants={fadeInUp}
            >
              {customHeading}
            </motion.h2>
            
            <motion.p
              className="text-lg neutral-700 mb-30 max-w-4xl mx-auto"
              initial="hidden"
              animate={headingInView ? "visible" : "hidden"}
              variants={fadeInUp}
            >
              {cityDescription}
            </motion.p>
            
            <motion.div
              className="box-button-preparing"
              ref={tabsRef}
              initial="hidden"
              animate={tabsInView ? "visible" : "hidden"}
              variants={{ visible: { transition: { staggerChildren: 0.1 } } }}
            >
              <ul className="nav nav-tabs justify-content-center" role="tablist">
                {servicesData.map((service, i) => (
                  <motion.li key={i} onClick={() => handleOnClick(i)} variants={buttonVariant}>
                    <a
                      className={
                        activeIndex === i ? "btn btn-neutral-100 active" : "btn btn-neutral-100"
                      }
                      data-bs-toggle="tab"
                      role="tab"
                    >
                      {service.tabTitle}
                    </a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </div>
          <motion.div
            className="block-group-preparing"
            ref={contentRef}
            initial="hidden"
            animate={contentInView ? "visible" : "hidden"}
            variants={fadeIn}
          >
            <div className="tab-content">
              {servicesData.map((service, i) => (
                <motion.div
                  key={`tab-${i}`}
                  className={
                    activeIndex === i ? "tab-pane fade show active" : "tab-pane fade"
                  }
                  id={`tab-${i}`}
                  initial="hidden"
                  animate={activeIndex === i ? "visible" : "hidden"}
                  variants={contentAnimation}
                >
                  <div className="item-preparing">
                    <motion.div
                      className={`item-preparing-left ${service.imageClass || ""}`}
                      variants={imageAnimation}
                    />
                    <div className="item-preparing-right">
                      <motion.h2 className="heading-2 mb-20" variants={fadeInUp}>
                        {service.title}
                      </motion.h2>
                      <motion.p className="text-lg neutral-700" variants={fadeInUp}>
                        {service.description}
                      </motion.p>
                      <motion.div className="box-list-check" variants={listAnimation}>
                        <ul className="list-check">
                          {service.features.map((feat, j) => (
                            <motion.li key={j} variants={listItemAnimation}>{feat}</motion.li>
                          ))}
                        </ul>
                      </motion.div>
                      {service.whyItMatters && (
                        <motion.p className="text-lg neutral-700 mb-20" variants={fadeInUp}>
                          {service.whyItMatters}
                        </motion.p>
                      )}
                      <Link className="btn btn-brand-4" href={service.link}>
                        Learn More
                        <svg width={23} height={8} viewBox="0 0 23 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M22.5 3.99934L18.9791 0.478516V3.30642H0.5V4.69236H18.9791V7.52031L22.5 3.99934Z"
                            fill="true"
                          />
                        </svg>
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </motion.section>
  );
}

// Export city data and helper functions
export { cityServicesData, defaultServicesData };

