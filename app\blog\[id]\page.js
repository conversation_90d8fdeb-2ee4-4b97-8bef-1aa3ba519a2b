'use client'
import Layout from "@/components/layout/Layout"
import data from "@/util/blog.json"
import Link from 'next/link'
import { useParams } from "next/navigation"
import { useEffect, useState } from "react"
import { motion } from "framer-motion"

export default function BlogDetails() {
    let Router = useParams()
    const [blogPost, setBlogPost] = useState(null)
    const [readingProgress, setReadingProgress] = useState(0)
    const [isBookmarked, setIsBookmarked] = useState(false)
    const [activeSection, setActiveSection] = useState(0)
    const id = Router.id

    useEffect(() => {
        setBlogPost(data.find((data) => data.id == id))
    }, [id])

    // Reading progress tracker
    useEffect(() => {
        const handleScroll = () => {
            const totalHeight = document.documentElement.scrollHeight - window.innerHeight
            const progress = (window.scrollY / totalHeight) * 100
            setReadingProgress(progress)
        }

        window.addEventListener('scroll', handleScroll)
        return () => window.removeEventListener('scroll', handleScroll)
    }, [])

    // Animation variants using theme patterns
    const fadeInUp = {
        initial: { opacity: 0, y: 50 },
        animate: { opacity: 1, y: 0 },
        transition: {
            duration: 0.7,
            type: "spring",
            stiffness: 100
        }
    }

    const staggerContainer = {
        animate: {
            transition: {
                staggerChildren: 0.15
            }
        }
    }

    return (
        <>
            <style jsx>{`
                @keyframes float {
                    0%, 100% { transform: translateY(0px) rotate(0deg); }
                    50% { transform: translateY(-20px) rotate(180deg); }
                }

                @keyframes shimmer {
                    0% { transform: translateX(-100%); }
                    100% { transform: translateX(100%); }
                }

                @keyframes pulse {
                    0% { box-shadow: 0 0 0 0 rgba(34, 209, 238, 0.7); }
                    70% { box-shadow: 0 0 0 10px rgba(34, 209, 238, 0); }
                    100% { box-shadow: 0 0 0 0 rgba(34, 209, 238, 0); }
                }

                .section-number {
                    animation: pulse 3s infinite;
                }

                .intro-pattern {
                    animation: float 6s ease-in-out infinite;
                }

                .section-divider {
                    position: relative;
                    overflow: hidden;
                }

                .section-divider::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent);
                    animation: shimmer 2s infinite;
                }

                .enhanced-bullet-item {
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .enhanced-bullet-item:hover {
                    transform: translateX(10px);
                    box-shadow: 0 4px 20px rgba(34, 209, 238, 0.15);
                }

                .subsection-card {
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                }

                .subsection-card:hover {
                    box-shadow: 0 8px 30px rgba(0,0,0,0.1);
                }

                .btn-floating {
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    backdrop-filter: blur(10px);
                }

                .btn-floating:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 8px 30px rgba(34, 209, 238, 0.3);
                }

                @media (max-width: 768px) {
                    .blog-actions {
                        right: 15px !important;
                        gap: 10px !important;
                    }

                    .btn-floating {
                        width: 40px !important;
                        height: 40px !important;
                        font-size: 16px !important;
                    }

                    .enhanced-bullet-item:hover {
                        transform: none;
                    }

                    .subsection-card:hover {
                        transform: none;
                    }
                }
            `}</style>
            {blogPost && (
                <Layout headerStyle={1} footerStyle={1} headerCls="header-style-2 header-style-4" >
                    {/* Reading Progress Bar */}
                    <div className="reading-progress-bar" style={{
                        position: 'fixed',
                        top: 0,
                        left: 0,
                        width: `${readingProgress}%`,
                        height: '4px',
                        background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                        zIndex: 9999,
                        transition: 'width 0.3s ease'
                    }}></div>

                    {/* Floating Action Buttons */}
                    <div className="blog-actions" style={{
                        position: 'fixed',
                        right: '30px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        zIndex: 1000,
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '15px'
                    }}>
                        <motion.button
                            className={`btn-floating ${isBookmarked ? 'bookmarked' : ''}`}
                            onClick={() => setIsBookmarked(!isBookmarked)}
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            style={{
                                width: '50px',
                                height: '50px',
                                borderRadius: '50%',
                                border: 'none',
                                background: isBookmarked ? 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)' : '#ffffff',
                                color: isBookmarked ? '#ffffff' : '#191919',
                                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '20px'
                            }}
                        >
                            {isBookmarked ? '❤️' : '🤍'}
                        </motion.button>

                        <motion.button
                            className="btn-floating"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            style={{
                                width: '50px',
                                height: '50px',
                                borderRadius: '50%',
                                border: 'none',
                                background: '#ffffff',
                                color: '#191919',
                                boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '20px'
                            }}
                        >
                            📤
                        </motion.button>
                    </div>

                    <section className="section-box box-content-blog-2 box-content-blog-post">
                        <div className="container">
                            <motion.div
                                className="text-center blog-head"
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                            >
                                <motion.span
                                    className="btn btn-brand-4-sm"
                                    whileHover={{ scale: 1.05 }}
                                    style={{ display: 'inline-block' }}
                                >
                                    {blogPost.category}
                                </motion.span>
                                <h2 className="heading-2 mb-20 mt-15">
                                    <span className="text-linear-3">{blogPost.title}</span>
                                </h2>
                                <p className="text-lg neutral-700">{blogPost.excerpt || "Explore this comprehensive guide to understand the latest trends and insights in digital marketing."}</p>
                                <div className="blog-meta mt-30">
                                    <div className="d-flex align-items-center justify-content-center">
                                        <div className="author-avatar me-3" style={{
                                            width: '50px',
                                            height: '50px',
                                            borderRadius: '50%',
                                            background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            color: '#ffffff',
                                            fontWeight: '600',
                                            fontSize: '18px'
                                        }}>
                                            {blogPost.author.charAt(0)}
                                        </div>
                                        <div className="author-info text-start">
                                            <div className="author neutral-900 fw-bold">{blogPost.author}</div>
                                            <div className="date neutral-600">{blogPost.date}</div>
                                        </div>
                                    </div>
                                </div>
                            </motion.div>
                            <div className="row">
                                <div className="col-lg-1" />
                                <div className="col-lg-10">
                                    <div className="box-content-detail-blog">
                                        <motion.div
                                            className="box-image-header"
                                            initial={{ opacity: 0, scale: 0.95 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            transition={{ duration: 0.8, delay: 0.3 }}
                                            whileHover={{ scale: 1.02 }}
                                            style={{
                                                borderRadius: '20px',
                                                overflow: 'hidden',
                                                boxShadow: '0 10px 40px rgba(0,0,0,0.1)'
                                            }}
                                        >
                                            <img alt="OMX Digital Blog" src={`/assets/imgs/page/blog/${blogPost.img}`} />
                                        </motion.div>
                                        <div className="box-detail-info">
                                            {/* Introduction */}
                                            {blogPost.content?.introduction && (
                                                <motion.div
                                                    className="blog-introduction mb-50"
                                                    initial={{ opacity: 0, y: 30 }}
                                                    animate={{ opacity: 1, y: 0 }}
                                                    transition={{ duration: 0.6, delay: 0.5 }}
                                                >
                                                    <div className="intro-card p-4 bg-3" style={{
                                                        borderRadius: '16px',
                                                        border: '2px solid #22d1ee',
                                                        position: 'relative',
                                                        overflow: 'hidden'
                                                    }}>
                                                        <div className="intro-pattern" style={{
                                                            position: 'absolute',
                                                            top: '-50%',
                                                            right: '-50%',
                                                            width: '100px',
                                                            height: '100px',
                                                            background: 'linear-gradient(45deg, rgba(34, 209, 238, 0.1), rgba(121, 198, 145, 0.1))',
                                                            borderRadius: '50%',
                                                            animation: 'float 6s ease-in-out infinite'
                                                        }}></div>
                                                        <div className="intro-icon mb-3" style={{
                                                            width: '60px',
                                                            height: '60px',
                                                            borderRadius: '50%',
                                                            background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            color: '#fff',
                                                            fontSize: '24px',
                                                            position: 'relative',
                                                            zIndex: 2
                                                        }}>
                                                            💡
                                                        </div>
                                                        <p className="text-lg mb-0 neutral-900" style={{
                                                            lineHeight: '1.8',
                                                            position: 'relative',
                                                            zIndex: 2
                                                        }}>
                                                            {blogPost.content.introduction}
                                                        </p>
                                                    </div>
                                                </motion.div>
                                            )}

                                            {/* Table of Contents */}
                                            {blogPost.content?.sections && blogPost.content.sections.length > 1 && (
                                                <motion.div
                                                    className="table-of-contents mb-50"
                                                    initial={{ opacity: 0, x: -30 }}
                                                    animate={{ opacity: 1, x: 0 }}
                                                    transition={{ duration: 0.6, delay: 0.6 }}
                                                >
                                                    <div className="toc-card p-4 bg-6" style={{
                                                        borderRadius: '16px',
                                                        border: '2px solid #ffeded',
                                                        position: 'relative',
                                                        overflow: 'hidden'
                                                    }}>
                                                        <div className="toc-header mb-3">
                                                            <h4 className="heading-4 mb-0" style={{ color: '#191919' }}>
                                                                📚 Table of Contents
                                                            </h4>
                                                        </div>
                                                        <ul className="toc-list" style={{ listStyle: 'none', padding: 0, margin: 0 }}>
                                                            {blogPost.content.sections.map((section, index) => (
                                                                <motion.li
                                                                    key={index}
                                                                    className="toc-item mb-2"
                                                                    initial={{ opacity: 0, x: -20 }}
                                                                    animate={{ opacity: 1, x: 0 }}
                                                                    transition={{ delay: 0.7 + index * 0.1 }}
                                                                    whileHover={{ x: 10 }}
                                                                >
                                                                    <a
                                                                        href={`#section-${index}`}
                                                                        className="toc-link d-flex align-items-center p-2 neutral-700"
                                                                        style={{
                                                                            textDecoration: 'none',
                                                                            borderRadius: '8px',
                                                                            transition: 'all 0.3s ease'
                                                                        }}
                                                                        onMouseEnter={(e) => {
                                                                            e.target.style.backgroundColor = '#ffffff'
                                                                            e.target.style.color = '#22d1ee'
                                                                            e.target.style.paddingLeft = '16px'
                                                                        }}
                                                                        onMouseLeave={(e) => {
                                                                            e.target.style.backgroundColor = 'transparent'
                                                                            e.target.style.color = '#727373'
                                                                            e.target.style.paddingLeft = '8px'
                                                                        }}
                                                                    >
                                                                        <span className="toc-number me-3" style={{
                                                                            width: '24px',
                                                                            height: '24px',
                                                                            borderRadius: '50%',
                                                                            background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                                                            color: '#fff',
                                                                            display: 'flex',
                                                                            alignItems: 'center',
                                                                            justifyContent: 'center',
                                                                            fontSize: '12px',
                                                                            fontWeight: 'bold',
                                                                            flexShrink: 0
                                                                        }}>
                                                                            {index + 1}
                                                                        </span>
                                                                        <span className="toc-title">{section.heading}</span>
                                                                    </a>
                                                                </motion.li>
                                                            ))}
                                                        </ul>
                                                    </div>
                                                </motion.div>
                                            )}

                                            {/* Main Content Sections */}
                                            <motion.div
                                                variants={staggerContainer}
                                                initial="initial"
                                                animate="animate"
                                            >
                                                {blogPost.content?.sections?.map((section, sectionIndex) => (
                                                    <motion.div
                                                        key={sectionIndex}
                                                        className="blog-section mb-60"
                                                        variants={fadeInUp}
                                                        whileHover={{
                                                            y: -5,
                                                            transition: { type: "spring", stiffness: 400, damping: 17 }
                                                        }}
                                                    >
                                                        <div className="section-card p-4" style={{
                                                            background: '#ffffff',
                                                            borderRadius: '20px',
                                                            border: '1px solid #eceef2',
                                                            boxShadow: '0 4px 20px rgba(0,0,0,0.05)',
                                                            position: 'relative',
                                                            overflow: 'hidden'
                                                        }}>
                                                            {/* Section Number Badge */}
                                                            <div className="section-number" style={{
                                                                position: 'absolute',
                                                                top: '20px',
                                                                right: '20px',
                                                                width: '40px',
                                                                height: '40px',
                                                                borderRadius: '50%',
                                                                background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                                                color: '#fff',
                                                                display: 'flex',
                                                                alignItems: 'center',
                                                                justifyContent: 'center',
                                                                fontSize: '16px',
                                                                fontWeight: 'bold'
                                                            }}>
                                                                {sectionIndex + 1}
                                                            </div>

                                                            <div className="section-header mb-30">
                                                                <h3 className="heading-3 mb-15 text-linear-3">
                                                                    {section.heading}
                                                                </h3>
                                                                <div className="section-divider" style={{
                                                                    width: '80px',
                                                                    height: '4px',
                                                                    background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                                                    borderRadius: '2px'
                                                                }}></div>
                                                            </div>

                                                            {/* Section content */}
                                                            {section.content && (
                                                                <div className="section-content mb-25">
                                                                    <p className="text-lg neutral-800" style={{ lineHeight: '1.8' }}>
                                                                        {section.content}
                                                                    </p>
                                                                </div>
                                                            )}

                                                            {/* Section bullet points */}
                                                            {section.bulletPoints && (
                                                                <div className="bullet-points-container mb-25">
                                                                    <ul className="enhanced-bullet-list" style={{ listStyle: 'none', padding: 0 }}>
                                                                        {section.bulletPoints.map((point, pointIndex) => (
                                                                            <motion.li
                                                                                key={pointIndex}
                                                                                className="enhanced-bullet-item mb-15 p-3 bg-1"
                                                                                initial={{ opacity: 0, x: -20 }}
                                                                                animate={{ opacity: 1, x: 0 }}
                                                                                transition={{ delay: pointIndex * 0.1 }}
                                                                                whileHover={{
                                                                                    x: 10,
                                                                                    backgroundColor: '#f7ffe4',
                                                                                    transition: { type: "spring", stiffness: 400, damping: 17 }
                                                                                }}
                                                                                style={{
                                                                                    position: 'relative',
                                                                                    paddingLeft: '40px',
                                                                                    borderRadius: '12px',
                                                                                    border: '1px solid #eceef2',
                                                                                    cursor: 'pointer'
                                                                                }}
                                                                            >
                                                                                <span className="bullet-icon" style={{
                                                                                    position: 'absolute',
                                                                                    left: '12px',
                                                                                    top: '50%',
                                                                                    transform: 'translateY(-50%)',
                                                                                    width: '20px',
                                                                                    height: '20px',
                                                                                    borderRadius: '50%',
                                                                                    background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                                                                    display: 'flex',
                                                                                    alignItems: 'center',
                                                                                    justifyContent: 'center',
                                                                                    color: '#fff',
                                                                                    fontSize: '12px',
                                                                                    fontWeight: 'bold'
                                                                                }}>
                                                                                    ✓
                                                                                </span>
                                                                                <span className="neutral-800">{point}</span>
                                                                            </motion.li>
                                                                        ))}
                                                                    </ul>
                                                                </div>
                                                            )}

                                                            {/* Section conclusion */}
                                                            {section.conclusion && (
                                                                <div className="section-conclusion">
                                                                    <div className="conclusion-card p-3 bg-5" style={{
                                                                        borderRadius: '12px',
                                                                        borderLeft: '4px solid #79c691',
                                                                        fontStyle: 'italic'
                                                                    }}>
                                                                        <p className="mb-0 neutral-800 fw-medium">
                                                                            💡 {section.conclusion}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            )}
                                                        </div>

                                                        {/* Sub-sections */}
                                                        {section.subSections?.map((subSection, subIndex) => (
                                                            <motion.div
                                                                key={subIndex}
                                                                className="blog-subsection mt-40 mb-30"
                                                                initial={{ opacity: 0, y: 20 }}
                                                                animate={{ opacity: 1, y: 0 }}
                                                                transition={{ delay: 0.2 + subIndex * 0.1 }}
                                                                whileHover={{
                                                                    y: -3,
                                                                    transition: { type: "spring", stiffness: 400, damping: 17 }
                                                                }}
                                                            >
                                                                <div className="subsection-card p-4 bg-2" style={{
                                                                    borderRadius: '16px',
                                                                    border: '1px solid #eceef2',
                                                                    boxShadow: '0 2px 15px rgba(0,0,0,0.05)',
                                                                    marginLeft: '30px'
                                                                }}>
                                                                    <h4 className="heading-4 mb-20" style={{
                                                                        color: '#22d1ee',
                                                                        borderBottom: '2px solid #eceef2',
                                                                        paddingBottom: '10px'
                                                                    }}>
                                                                        📋 {subSection.subHeading}
                                                                    </h4>
                                                                    <p className="mb-20 neutral-800" style={{ lineHeight: '1.8' }}>
                                                                        {subSection.content}
                                                                    </p>

                                                                    {/* Sub-section bullet points */}
                                                                    {subSection.bulletPoints && (
                                                                        <ul className="subsection-bullets mb-20" style={{ listStyle: 'none', padding: 0 }}>
                                                                            {subSection.bulletPoints.map((point, pointIndex) => (
                                                                                <li key={pointIndex} className="mb-10 p-2" style={{
                                                                                    position: 'relative',
                                                                                    paddingLeft: '30px',
                                                                                    borderRadius: '8px',
                                                                                    backgroundColor: '#ffffff',
                                                                                    border: '1px solid #f0f0f0'
                                                                                }}>
                                                                                    <span style={{
                                                                                        position: 'absolute',
                                                                                        left: '8px',
                                                                                        top: '50%',
                                                                                        transform: 'translateY(-50%)',
                                                                                        width: '16px',
                                                                                        height: '16px',
                                                                                        borderRadius: '50%',
                                                                                        background: '#22d1ee',
                                                                                        display: 'flex',
                                                                                        alignItems: 'center',
                                                                                        justifyContent: 'center',
                                                                                        color: '#fff',
                                                                                        fontSize: '10px'
                                                                                    }}>
                                                                                        •
                                                                                    </span>
                                                                                    <span className="neutral-700">{point}</span>
                                                                                </li>
                                                                            ))}
                                                                        </ul>
                                                                    )}

                                                                    {/* Sub-section conclusion */}
                                                                    {subSection.conclusion && (
                                                                        <p className="mb-0 fw-medium" style={{
                                                                            color: '#79c691',
                                                                            fontStyle: 'italic'
                                                                        }}>
                                                                            ✨ {subSection.conclusion}
                                                                        </p>
                                                                    )}
                                                                </div>
                                                            </motion.div>
                                                        ))}
                                                    </motion.div>
                                                ))}
                                            </motion.div>

                                            {/* Call to Action */}
                                            {blogPost.content?.callToAction && (
                                                <motion.div
                                                    className="blog-cta mt-60"
                                                    initial={{ opacity: 0, scale: 0.95 }}
                                                    animate={{ opacity: 1, scale: 1 }}
                                                    transition={{ duration: 0.6, delay: 0.5 }}
                                                    whileHover={{
                                                        scale: 1.02,
                                                        transition: { type: "spring", stiffness: 400, damping: 17 }
                                                    }}
                                                >
                                                    <div className="cta-card text-center p-5" style={{
                                                        background: 'linear-gradient(236deg, #22d1ee 0%, #79c691 100%)',
                                                        borderRadius: '24px',
                                                        color: '#fff',
                                                        position: 'relative',
                                                        overflow: 'hidden',
                                                        boxShadow: '0 15px 50px rgba(34, 209, 238, 0.3)'
                                                    }}>
                                                        {/* Animated Background Pattern */}
                                                        <div className="cta-pattern position-absolute w-100 h-100" style={{
                                                            top: 0,
                                                            left: 0,
                                                            backgroundImage: 'radial-gradient(circle at 20% 50%, rgba(255,255,255,0.1) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%)',
                                                            zIndex: 1,
                                                            animation: 'blur-move 10s ease infinite'
                                                        }}></div>

                                                        <div className="cta-content position-relative" style={{ zIndex: 2 }}>
                                                            <motion.div
                                                                className="cta-icon mb-4"
                                                                animate={{
                                                                    rotate: [0, 10, -10, 0],
                                                                    scale: [1, 1.1, 1]
                                                                }}
                                                                transition={{
                                                                    duration: 2,
                                                                    repeat: Infinity,
                                                                    repeatType: "reverse"
                                                                }}
                                                                style={{
                                                                    width: '100px',
                                                                    height: '100px',
                                                                    borderRadius: '50%',
                                                                    background: 'rgba(255,255,255,0.2)',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    margin: '0 auto',
                                                                    fontSize: '40px',
                                                                    backdropFilter: 'blur(10px)'
                                                                }}
                                                            >
                                                                🚀
                                                            </motion.div>
                                                            <h4 className="heading-4 mb-20 text-white fw-bold">
                                                                {blogPost.content.callToAction.heading}
                                                            </h4>
                                                            <p className="mb-30 text-white" style={{
                                                                fontSize: '18px',
                                                                opacity: 0.95,
                                                                lineHeight: '1.6'
                                                            }}>
                                                                {blogPost.content.callToAction.content}
                                                            </p>
                                                            <motion.div
                                                                whileHover={{ scale: 1.05 }}
                                                                whileTap={{ scale: 0.95 }}
                                                            >
                                                                <Link href="/form" className="btn btn-black btn-lg px-5 py-3" style={{
                                                                    borderRadius: '50px',
                                                                    fontWeight: '600',
                                                                    fontSize: '18px',
                                                                    boxShadow: '0 8px 30px rgba(0,0,0,0.2)',
                                                                    border: '2px solid rgba(255,255,255,0.2)',
                                                                    backdropFilter: 'blur(10px)'
                                                                }}>
                                                                    Get Started Today
                                                                    <svg width={22} height={22} viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg" className="ms-2">
                                                                        <path d="M22 11.0003L18.4791 7.47949V10.3074H0V11.6933H18.4791V14.5213L22 11.0003Z" fill="currentColor"></path>
                                                                    </svg>
                                                                </Link>
                                                            </motion.div>
                                                        </div>
                                                    </div>
                                                </motion.div>
                                            )}

                                            {/* Fallback content for blogs without structured content */}
                                            {!blogPost.content && (
                                                <div className="fallback-content">
                                                    <p>Design comps, layouts, wireframes—will your clients accept that you go about things the facile way? Authorities in our business will tell in no uncertain terms that Lorem Ipsum is that huge, huge no no to forswear forever.</p>
                                                    <p>Not so fast, I'd say, there are some redeeming factors in favor of greeking text, as its use is merely the symptom of a worse problem to take into consideration.</p>
                                                    <p>The toppings you may chose for that TV dinner pizza slice when you forgot to shop for foods, the paint you may slap on your face to impress the new boss is your business. But what about your daily bread?</p>
                                                    <img src="/assets/imgs/page/blog/img-detail2.png" alt="Blog Detail" />
                                                    <p>Design comps, layouts, wireframes—will your clients accept that you go about things the facile way? Authorities in our business will tell in no uncertain terms that Lorem Ipsum is that huge, huge no no to forswear forever.</p>
                                                    <blockquote>Design comps, layouts, wireframes—we believe that clients will surely accept that you go about things the facile way. It's a matter of time.</blockquote>
                                                    <p>Not so fast, I'd say, there are some redeeming factors in favor of greeking text, as its use is merely the symptom of a worse problem to take into consideration.</p>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                    <section className="section-box box-content-recommended">
                        <div className="container">
                            <div className="text-center">
                                <h2 className="mb-55">Recommended Articles</h2>
                            </div>
                            <div className="row">
                                <div className="col-lg-4">
                                    <div className="card-news-style-2">
                                        <div className="card-image">
                                            <Link href="/blog-post">
                                                <img src="/assets/imgs/page/blog/detail.png" alt="OMX Digital" />
                                            </Link>
                                        </div>
                                        <div className="card-info">
                                            <div className="card-meta">
                                                <Link className="btn btn-tag-sm" href="/blog-post">Technology</Link>
                                                <span className="date-post">16 October 2023</span>
                                            </div>
                                            <div className="card-title">
                                                <Link className="link-new" href="/blog-post">Savvy brand marketing: from branding basics to key strategies</Link>
                                            </div>
                                            <div className="card-more">
                                                <Link className="btn btn-learmore-2" href="/blog-post">Read More
                                                    <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clipPath="url(#clip0_599_4830)">
                                                            <path d="M10.6537 3.8149L1.71801 12.7506L0.25 11.2826L9.18469 2.3469H1.31V0.270508H12.7301V11.6906H10.6537V3.8149Z" fill="true" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_599_4830">
                                                                <rect width={13} height={13} fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-lg-4">
                                    <div className="card-news-style-2">
                                        <div className="card-image">
                                            <Link href="/blog-post">
                                                <img src="/assets/imgs/page/blog/detail2.png" alt="OMX Digital" />
                                            </Link>
                                        </div>
                                        <div className="card-info">
                                            <div className="card-meta">
                                                <Link className="btn btn-tag-sm" href="/blog-post">Technology</Link>
                                                <span className="date-post">16 October 2023</span>
                                            </div>
                                            <div className="card-title">
                                                <Link className="link-new" href="/blog-post">110 drawing ideas to improve your skills you must know in this year</Link>
                                            </div>
                                            <div className="card-more">
                                                <Link className="btn btn-learmore-2" href="/blog-post">Read More
                                                    <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clipPath="url(#clip0_599_4830)">
                                                            <path d="M10.6537 3.8149L1.71801 12.7506L0.25 11.2826L9.18469 2.3469H1.31V0.270508H12.7301V11.6906H10.6537V3.8149Z" fill="true" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_599_4830">
                                                                <rect width={13} height={13} fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className="col-lg-4">
                                    <div className="card-news-style-2">
                                        <div className="card-image">
                                            <Link href="/blog-post">
                                                <img src="/assets/imgs/page/blog/detail3.png" alt="OMX Digital" />
                                            </Link>
                                        </div>
                                        <div className="card-info">
                                            <div className="card-meta">
                                                <Link className="btn btn-tag-sm" href="/blog-post">Technology</Link>
                                                <span className="date-post">16 October 2023</span>
                                            </div>
                                            <div className="card-title">
                                                <Link className="link-new" href="/blog-post">Perfect product images with Generative AI in OMX Digital platform</Link>
                                            </div>
                                            <div className="card-more">
                                                <Link className="btn btn-learmore-2" href="/blog-post">Read More
                                                    <svg width={13} height={13} viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <g clipPath="url(#clip0_599_4830)">
                                                            <path d="M10.6537 3.8149L1.71801 12.7506L0.25 11.2826L9.18469 2.3469H1.31V0.270508H12.7301V11.6906H10.6537V3.8149Z" fill="true" />
                                                        </g>
                                                        <defs>
                                                            <clipPath id="clip0_599_4830">
                                                                <rect width={13} height={13} fill="white" />
                                                            </clipPath>
                                                        </defs>
                                                    </svg>
                                                </Link>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </Layout>
            )}
        </>
    )
}
