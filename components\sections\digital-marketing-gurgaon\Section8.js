"use client";
import Link from "next/link";
import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

export default function Section8() {
  // Refs for scroll detection
  const sectionRef = useRef(null);
  const contentRef = useRef(null);
  const imageRef = useRef(null);
  
  // Check if elements are in view
  const isSectionInView = useInView(sectionRef, { once: true, amount: 0.2 });
  const isContentInView = useInView(contentRef, { once: true, amount: 0.4 });
  const isImageInView = useInView(imageRef, { once: true, amount: 0.3 });

  // Main container animation
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { 
        duration: 0.7,
        staggerChildren: 0.2
      } 
    }
  };

  // Content animations
  const contentVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.15
      } 
    }
  };

  // Element animations
  const textVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 } 
    }
  };

  // Button animation
  const buttonVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: { 
      opacity: 1, 
      scale: 1,
      transition: { 
        duration: 0.5,
        type: "spring",
        stiffness: 200
      } 
    },
    hover: { 
      scale: 1.05,
      transition: { 
        type: "spring", 
        stiffness: 400, 
        damping: 10 
      } 
    }
  };

  // Image animation
  const imageVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { 
        duration: 0.7,
        type: "spring",
        stiffness: 100
      } 
    }
  };

  // Arrow animation
  const arrowVariants = {
    hover: { 
      x: 5,
      transition: { 
        type: "spring", 
        stiffness: 300, 
        damping: 10,
        repeat: Infinity,
        repeatType: "reverse"
      } 
    }
  };

  return (
    <>
      <motion.section 
        className="section-box box-more-question"
        ref={sectionRef}
        initial="hidden"
        animate={isSectionInView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <div className="container">
          <motion.div 
            className="block-more-question"
            variants={containerVariants}
          >
            <motion.div 
              className="question-left"
              ref={contentRef}
              variants={contentVariants}
              initial="hidden"
              animate={isContentInView ? "visible" : "hidden"}
            >
              <motion.h3 
                className="mb-10"
                variants={textVariants}
              >
                Stop wasting money on marketing that doesn't work. Let's build
                campaigns that convert.
              </motion.h3>
              
              <motion.p 
                className="text-lg mb-20"
                variants={textVariants}
              >
                Ready to accelerate? Partner with the best digital marketing agency in Gurgaon and boost growth with AI automation for business Gurgaon.
              </motion.p>
              
              <motion.div
                variants={buttonVariants}
                whileHover="hover"
              >
                <Link className="btn btn-brand-4" href="/contact">
                  <motion.span
                    initial={{ opacity: 1 }}
                  >
                    Get Your Free Call
                  </motion.span>
                  
                  <motion.svg
                    width={23}
                    height={22}
                    viewBox="0 0 23 22"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    variants={arrowVariants}
                  >
                    <g clipPath="url(#clip0_449_3780)">
                      <path
                        d="M22.5 10.9993L18.9791 7.47852V10.3064H0.5V11.6924H18.9791V14.5203L22.5 10.9993Z"
                        fill="#191919"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_449_3780">
                        <rect
                          width={22}
                          height={22}
                          fill="white"
                          transform="translate(0.5)"
                        />
                      </clipPath>
                    </defs>
                  </motion.svg>
                </Link>
              </motion.div>
            </motion.div>
            
            <motion.div 
              className="question-right"
              ref={imageRef}
              variants={imageVariants}
              initial="hidden"
              animate={isImageInView ? "visible" : "hidden"}
            >
              <motion.img
                src="/assets/imgs/page/homepage4/bg-question.png"
                alt="WhatsApp API provider Gurgaon - Digital marketing consultation and expert guidance"
                whileHover={{
                  scale: 1.03,
                  transition: { duration: 0.3 }
                }}
              />
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    </>
  );
}